# 🧹 Guia de Limpeza Geral do Repositório

Este documento descreve o processo completo de limpeza geral do repositório Lisa Workspace, removendo todos os arquivos não versionados para garantir um ambiente limpo e consistente.

## 📋 Quando Fazer Limpeza Geral

A limpeza geral deve ser feita quando:

- **Problemas de build persistentes** que não são resolvidos com reinstalação simples
- **Inconsistências entre ambientes** de desenvolvimento
- **Preparação para novos desenvolvedores** garantindo setup limpo
- **Após mudanças significativas** na estrutura do projeto
- **Problemas de cache** do TypeScript, Turbo ou outras ferramentas
- **Migração de versões** de Node.js ou ferramentas principais

## ⚠️ Pré-requisitos e Avisos

### Antes de Começar

1. **Commit todas as mudanças** importantes:

   ```bash
   git add .
   git commit -m "feat: save work before cleanup"
   git push
   ```

2. **Backup de arquivos .env** se necessário:

   ```bash
   # Opcional: fazer backup dos .env
   find . -name ".env" -not -path "./node_modules/*" -exec cp {} {}.backup \;
   ```

3. **Verificar status do git**:
   ```bash
   git status
   # Certifique-se de que não há arquivos importantes não commitados
   ```

### ⚠️ Avisos Importantes

- **Este processo é irreversível** para arquivos não versionados
- **Todos os node_modules serão removidos** (será necessário reinstalar)
- **Todos os builds serão removidos** (será necessário rebuildar)
- **Caches serão limpos** (primeira execução será mais lenta)

## 🧹 Processo de Limpeza Completa

### Etapa 1: Limpeza de Dependências

```bash
# Remover todos os node_modules (root e subprojetos)
rm -rf node_modules
find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true

# Limpar cache do pnpm
pnpm store prune
```

### Etapa 2: Limpeza de Builds e Artefatos

```bash
# Remover diretórios de build
rm -rf dist
find . -name "dist" -type d -not -path "./node_modules/*" -exec rm -rf {} + 2>/dev/null || true

# Remover arquivos TypeScript build info
find . -name "*.tsbuildinfo" -delete
find . -name "tsconfig.tsbuildinfo" -delete
```

### Etapa 3: Limpeza de Caches

```bash
# Remover cache do Turbo
rm -rf .turbo
find . -name ".turbo" -type d -exec rm -rf {} + 2>/dev/null || true

# Remover cache do Mastra (se existir)
rm -rf .mastra
find . -name ".mastra" -type d -exec rm -rf {} + 2>/dev/null || true

# Remover outros caches comuns
rm -rf .next
rm -rf .vite
find . -name ".next" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".vite" -type d -exec rm -rf {} + 2>/dev/null || true
```

### Etapa 4: Limpeza de Arquivos Temporários

```bash
# Remover arquivos de banco de dados temporários
find . -name "*.db" -delete
find . -name "*.sqlite" -delete

# Remover logs
find . -name "*.log" -delete
find . -name "npm-debug.log*" -delete
find . -name "yarn-debug.log*" -delete
find . -name "yarn-error.log*" -delete

# Remover arquivos temporários do sistema
find . -name ".DS_Store" -delete
find . -name "Thumbs.db" -delete
```

### Etapa 5: Limpeza de Coverage e Testes

```bash
# Remover coverage de testes
rm -rf coverage
find . -name "coverage" -type d -exec rm -rf {} + 2>/dev/null || true

# Remover arquivos de teste temporários
find . -name "*.test.js.map" -delete
find . -name "*.spec.js.map" -delete
```

## 🚀 Script de Limpeza Automatizada

Para facilitar o processo, você pode usar este script completo:

```bash
#!/bin/bash
# Script: clean-repository.sh

echo "🧹 Iniciando limpeza geral do repositório..."

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ] || [ ! -f "pnpm-workspace.yaml" ]; then
    echo "❌ Erro: Execute este script no diretório raiz do workspace"
    exit 1
fi

# Verificar status do git
if [ -n "$(git status --porcelain)" ]; then
    echo "⚠️  Aviso: Há arquivos não commitados. Considere fazer commit antes da limpeza."
    read -p "Continuar mesmo assim? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "🗑️  Removendo node_modules..."
rm -rf node_modules
find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true

echo "🗑️  Removendo builds..."
rm -rf dist
find . -name "dist" -type d -not -path "./node_modules/*" -exec rm -rf {} + 2>/dev/null || true

echo "🗑️  Removendo arquivos TypeScript build info..."
find . -name "*.tsbuildinfo" -delete

echo "🗑️  Removendo caches..."
rm -rf .turbo .mastra .next .vite
find . -name ".turbo" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".mastra" -type d -exec rm -rf {} + 2>/dev/null || true

echo "🗑️  Removendo arquivos temporários..."
find . -name "*.db" -delete
find . -name "*.log" -delete
find . -name ".DS_Store" -delete

echo "🧹 Limpando cache do pnpm..."
pnpm store prune

echo "✅ Limpeza completa finalizada!"
echo "📝 Próximos passos:"
echo "   1. pnpm install"
echo "   2. pnpm build"
echo "   3. Configurar arquivos .env se necessário"
```

## 📊 Verificação Pós-Limpeza

Após a limpeza, verifique se tudo foi removido corretamente:

```bash
# Verificar se não há node_modules restantes
find . -name "node_modules" -type d

# Verificar se não há builds restantes
find . -name "dist" -type d

# Verificar se não há .tsbuildinfo restantes
find . -name "*.tsbuildinfo"

# Verificar tamanho do diretório (deve estar significativamente menor)
du -sh .
```

## 🔄 Próximos Passos Após Limpeza

1. **Reinstalar dependências**:

   ```bash
   pnpm install
   ```

2. **Rebuild do projeto**:

   ```bash
   pnpm build
   ```

3. **Verificar configurações**:

   ```bash
   # Verificar se .env files existem
   find . -name ".env.example" -exec dirname {} \; | sort | uniq
   ```

4. **Executar testes básicos**:
   ```bash
   pnpm lint
   pnpm type-check
   ```

## 🚨 Troubleshooting

### Problemas Comuns

1. **Permissões negadas**:

   ```bash
   # No macOS/Linux, pode ser necessário usar sudo para alguns arquivos
   sudo find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
   ```

2. **Arquivos em uso**:

   ```bash
   # Parar todos os processos Node.js
   pkill -f node
   # Aguardar alguns segundos e tentar novamente
   ```

3. **Espaço em disco insuficiente**:
   ```bash
   # Verificar espaço disponível
   df -h
   # Limpar cache do sistema se necessário
   ```

## 📝 Logs e Auditoria

Para manter registro das limpezas:

```bash
# Criar log da limpeza
echo "$(date): Limpeza geral executada por $(whoami)" >> .cleanup-history.log
```

## 🔗 Referências

- [QUICK_START_GUIDE.md](../setup/QUICK_START_GUIDE.md) - Setup após limpeza
- [TROUBLESHOOTING.md](../setup/TROUBLESHOOTING.md) - Resolução de problemas
- [GENERAL_TESTING_GUIDE.md](./GENERAL_TESTING_GUIDE.md) - Testes após setup
