# 🧪 Guia de Teste Geral do Lisa Workspace

Este documento descreve o processo completo de teste geral do repositório Lisa Workspace, garantindo que tudo está funcionando corretamente após mudanças, setup ou para validação geral do projeto.

## 📋 Quando Executar Teste Geral

O teste geral deve ser executado:

- **Após setup inicial** do projeto
- **Após limpeza geral** do repositório
- **Antes de fazer deploy** ou release
- **Após mudanças significativas** no código
- **Para validar ambiente** de novos desenvolvedores
- **Antes de criar pull requests** importantes
- **Periodicamente** para garantir saúde do projeto

## 🎯 Objetivos do Teste Geral

1. **Qualidade de Código**: Verificar lint, formatação e tipos
2. **Build**: Garantir que todos os projetos compilam corretamente
3. **Execução**: Verificar se serviços iniciam sem erros
4. **Docker**: Validar builds de containers
5. **Integração**: Testar comunicação entre serviços

## 🚀 Processo de Teste Geral Completo

### Fase 1: Verificação de Qualidade de Código

#### 1.1 Linting

```bash
# Verificar problemas de lint em todo o workspace
pnpm lint

# Se houver problemas, tentar correção automática
pnpm lint:fix
```

**Critério de Sucesso**: ✅ Nenhum erro de lint

#### 1.2 Formatação

```bash
# Verificar formatação do código
pnpm format:check

# Se houver problemas, corrigir automaticamente
pnpm format:write
```

**Critério de Sucesso**: ✅ Código formatado corretamente

#### 1.3 Verificação de Tipos TypeScript

```bash
# Verificar tipos em todo o workspace
pnpm type-check
```

**Critério de Sucesso**: ✅ Nenhum erro de tipo TypeScript

### Fase 2: Build Completo

#### 2.1 Build de Todas as Bibliotecas e Apps

```bash
# Build completo do workspace
pnpm build
```

**Critério de Sucesso**: ✅ Todos os projetos compilam sem erro

#### 2.2 Build Específico (se necessário)

```bash
# Build específico de APIs principais
pnpm build:ai.api
pnpm build:twilio.api
pnpm build:smarters.api
```

**Critério de Sucesso**: ✅ Builds específicos funcionam

### Fase 3: Teste de Execução em Desenvolvimento

#### 3.1 Teste de Inicialização Individual

```bash
# Testar se cada serviço inicia sem erros (executar em terminais separados)

# APIs principais
pnpm dev:ai.api          # Porta 4112
pnpm dev:twilio.api      # Porta 3005
pnpm dev:smarters.api    # Porta 3006
pnpm dev:copilotkit.api  # Porta 8081

# Workers
pnpm dev:chatwoot.worker
pnpm dev:aocubo.worker

# Test Clients
pnpm dev:copilotkit.test-client  # Porta 5173
pnpm dev:webchat.test-client     # Porta 3001
pnpm dev:agui.test-server        # Porta 4013
```

**Critério de Sucesso**: ✅ Cada serviço inicia sem erros críticos

#### 3.2 Teste de Inicialização Geral

```bash
# Testar inicialização de todos os serviços
pnpm dev
```

**Critério de Sucesso**: ✅ Todos os serviços iniciam simultaneamente

#### 3.3 Verificação de Saúde dos Serviços

```bash
# Verificar se as portas estão sendo usadas
lsof -i :4112  # ai.api
lsof -i :3005  # twilio.api
lsof -i :3006  # smarters.api
lsof -i :8081  # copilotkit.api
lsof -i :5173  # copilotkit.test-client
lsof -i :3001  # webchat.test-client
lsof -i :4013  # agui.test-server
```

**Critério de Sucesso**: ✅ Portas estão sendo utilizadas pelos serviços corretos

### Fase 4: Teste de Builds Docker

#### 4.1 Build das Imagens Docker Principais

```bash
# Build das imagens Docker das APIs principais
pnpm docker:build:ai.api
pnpm docker:build:twilio.api
pnpm docker:build:smarters.api
```

**Critério de Sucesso**: ✅ Todas as imagens Docker são criadas com sucesso

#### 4.2 Verificação das Imagens

```bash
# Listar imagens criadas
docker images | grep lisa

# Verificar tamanhos das imagens (devem ser razoáveis)
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep lisa
```

**Critério de Sucesso**: ✅ Imagens existem e têm tamanhos apropriados

### Fase 5: Testes Funcionais Básicos

#### 5.1 Teste do AI Workflow

```bash
# Executar teste específico do AI
pnpm ai  # Alias para pnpm dev:ai.test
```

**Critério de Sucesso**: ✅ Workflows AI executam sem erro

#### 5.2 Verificação de Endpoints (se aplicável)

```bash
# Verificar se APIs respondem (com serviços rodando)
curl -f http://localhost:4112/health || echo "AI API não responde"
curl -f http://localhost:3005/health || echo "Twilio API não responde"
curl -f http://localhost:3006/health || echo "Smarters API não responde"
curl -f http://localhost:8081/health || echo "CopilotKit API não responde"
```

**Critério de Sucesso**: ✅ APIs principais respondem

## 🤖 Script de Teste Geral Automatizado

Para facilitar o processo, você pode usar este script:

```bash
#!/bin/bash
# Script: test-general.sh

echo "🧪 Iniciando Teste Geral do Lisa Workspace..."
echo "============================================="

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ] || [ ! -f "pnpm-workspace.yaml" ]; then
    echo "❌ Erro: Execute este script no diretório raiz do workspace"
    exit 1
fi

echo ""
echo "📋 Fase 1: Verificação de Qualidade de Código"
echo "---------------------------------------------"

echo "🔍 Executando lint..."
if pnpm lint; then
    echo "✅ Lint passou"
else
    echo "❌ Lint falhou"
    exit 1
fi

echo "🎨 Verificando formatação..."
if pnpm format:check; then
    echo "✅ Formatação correta"
else
    echo "⚠️  Formatação incorreta, corrigindo..."
    pnpm format:write
fi

echo "🔧 Verificando tipos TypeScript..."
if pnpm type-check; then
    echo "✅ Tipos corretos"
else
    echo "❌ Erros de tipo encontrados"
    exit 1
fi

echo ""
echo "🏗️  Fase 2: Build Completo"
echo "-------------------------"

echo "🔨 Executando build completo..."
if pnpm build; then
    echo "✅ Build completo passou"
else
    echo "❌ Build falhou"
    exit 1
fi

echo ""
echo "🐳 Fase 3: Teste de Builds Docker"
echo "--------------------------------"

echo "🔨 Testando build Docker ai.api..."
if pnpm docker:build:ai.api; then
    echo "✅ Docker build ai.api passou"
else
    echo "❌ Docker build ai.api falhou"
    exit 1
fi

echo "🔨 Testando build Docker twilio.api..."
if pnpm docker:build:twilio.api; then
    echo "✅ Docker build twilio.api passou"
else
    echo "❌ Docker build twilio.api falhou"
    exit 1
fi

echo "🔨 Testando build Docker smarters.api..."
if pnpm docker:build:smarters.api; then
    echo "✅ Docker build smarters.api passou"
else
    echo "❌ Docker build smarters.api falhou"
    exit 1
fi

echo ""
echo "✅ Teste Geral Completo - SUCESSO!"
echo "=================================="
echo ""
echo "📝 Próximos passos recomendados:"
echo "   1. Testar execução: pnpm dev"
echo "   2. Verificar endpoints manualmente"
echo "   3. Executar testes funcionais: pnpm ai"
echo ""
echo "🎉 Todos os testes passaram!"
```

## 📊 Checklist de Teste Geral

Use este checklist para garantir que todos os testes foram executados:

### ✅ Qualidade de Código

- [ ] `pnpm lint` - sem erros
- [ ] `pnpm format:check` - formatação correta
- [ ] `pnpm type-check` - sem erros de tipo

### ✅ Build

- [ ] `pnpm build` - build completo sem erros
- [ ] Builds específicos funcionam se necessário

### ✅ Execução

- [ ] `pnpm dev` - todos os serviços iniciam
- [ ] Portas corretas estão sendo utilizadas
- [ ] Nenhum erro crítico nos logs

### ✅ Docker

- [ ] `pnpm docker:build:ai.api` - sucesso
- [ ] `pnpm docker:build:twilio.api` - sucesso
- [ ] `pnpm docker:build:smarters.api` - sucesso
- [ ] Imagens Docker criadas corretamente

### ✅ Funcional

- [ ] `pnpm ai` - workflows AI funcionam
- [ ] APIs respondem a health checks
- [ ] Test clients carregam sem erro

## 🚨 Troubleshooting

### Problemas Comuns e Soluções

1. **Erro de Lint**:

   ```bash
   pnpm lint:fix
   ```

2. **Erro de Build**:

   ```bash
   # Limpar e tentar novamente
   pnpm clean
   pnpm install
   pnpm build
   ```

3. **Erro de Docker Build**:

   ```bash
   # Verificar se Docker está rodando
   docker --version

   # Limpar cache do Docker se necessário
   docker system prune -f
   ```

4. **Serviços não iniciam**:

   ```bash
   # Verificar se portas estão livres
   lsof -i :4112

   # Matar processos se necessário
   pkill -f node
   ```

## 🔗 Referências

- [REPOSITORY_CLEANUP_GUIDE.md](./REPOSITORY_CLEANUP_GUIDE.md) - Limpeza antes dos testes
- [QUICK_START_GUIDE.md](../setup/QUICK_START_GUIDE.md) - Setup inicial
- [TROUBLESHOOTING.md](../setup/TROUBLESHOOTING.md) - Resolução de problemas
