# 📚 Lisa Workspace - Documentação

Bem-vindo à documentação completa do Lisa Workspace! Esta documentação está organizada por categorias para facilitar a navegação e encontrar rapidamente as informações que você precisa.

## 🗂️ Estrutura da Documentação

### 🚀 Setup e Configuração Inicial

**Pasta: [`setup/`](./setup/)**

- **[<PERSON><PERSON><PERSON> de Início Rápido](./setup/QUICK_START_GUIDE.md)** - Instruções completas para configurar e executar todos os serviços do zero
- **[Resultados do Setup](./setup/SETUP_RESULTS.md)** - Documentação dos resultados de configuração bem-sucedida
- **[Troubleshooting](./setup/TROUBLESHOOTING.md)** - Guia de resolução de problemas comuns

### 💻 Desenvolvimento

**Pasta: [`development/`](./development/)**

- **[Workflow de Desenvolvimento](./development/DEVELOPMENT_WORKFLOW.md)** - Processo de desenvolvimento e boas práticas
- **[Desenvolvimento WebChat](./development/WEBCHAT_DEVELOPMENT.md)** - Guia específico para desenvolvimento do WebChat
- **[Workflow Completo](./development/WORKFLOW_COMPLETO.md)** - Documentação completa dos workflows

### 🚀 Deploy e CI/CD

**Pasta: [`deployment/`](./deployment/)**

- **[Guia de CI/CD](./deployment/CI_CD_GUIDE.md)** - Configuração e uso do pipeline de CI/CD
- **[Guia de Deploy](./deployment/DEPLOY_GUIDE.md)** - Instruções para deploy em produção
- **[Referência Rápida de Deploy](./deployment/QUICK_DEPLOY_REFERENCE.md)** - Comandos rápidos para deploy
- **[Sistema de Validação de Tags](./deployment/TAG_VALIDATION_SYSTEM.md)** - Sistema de validação de tags Git

### 🔧 Manutenção e Configuração

**Pasta: [`maintenance/`](./maintenance/)**

- **[Plano de Padronização ESLint](./maintenance/ESLINT_STANDARDIZATION_PLAN.md)** - Estratégia para padronizar ESLint
- **[Resumo de Correções ESLint](./maintenance/ESLINT_FIX_SUMMARY.md)** - Resumo das correções aplicadas
- **[Plano de Consolidação TSConfig](./maintenance/TSCONFIG_CONSOLIDATION_PLAN.md)** - Estratégia para consolidar configurações TypeScript
- **[Consolidação TSConfig Completa](./maintenance/TSCONFIG_CONSOLIDATION_COMPLETED.md)** - Documentação da consolidação realizada

### 📊 Análises e Resultados

**Pasta: [`analysis/`](./analysis/)**

- **[Análise de Problemas de Runtime](./analysis/RUNTIME_ISSUES_ANALYSIS.md)** - Análise detalhada de problemas de execução
- **[Resultados de Testes de Comandos](./analysis/RUN_COMMANDS_TEST_RESULTS.md)** - Resultados dos testes de comandos do projeto

## 🎯 Guias de Início Rápido

### Para Novos Desenvolvedores

1. **Comece aqui:** [Guia de Início Rápido](./setup/QUICK_START_GUIDE.md)
2. **Se tiver problemas:** [Troubleshooting](./setup/TROUBLESHOOTING.md)
3. **Para desenvolvimento:** [Workflow de Desenvolvimento](./development/DEVELOPMENT_WORKFLOW.md)

### Para Deploy

1. **Deploy rápido:** [Referência Rápida de Deploy](./deployment/QUICK_DEPLOY_REFERENCE.md)
2. **Setup completo:** [Guia de Deploy](./deployment/DEPLOY_GUIDE.md)
3. **CI/CD:** [Guia de CI/CD](./deployment/CI_CD_GUIDE.md)

### Para Manutenção

1. **Problemas de código:** [Plano ESLint](./maintenance/ESLINT_STANDARDIZATION_PLAN.md)
2. **Configuração TypeScript:** [Consolidação TSConfig](./maintenance/TSCONFIG_CONSOLIDATION_PLAN.md)
3. **Análise de problemas:** [Análise de Runtime](./analysis/RUNTIME_ISSUES_ANALYSIS.md)

## 🏗️ Arquitetura do Projeto

O Lisa Workspace é um monorepo que contém:

### APIs (Backend Services)

- **ai.api** (Porta 4112) - API principal com Mastra/OpenAI
- **twilio.api** (Porta 3005) - Integração com Twilio
- **smarters.api** (Porta 3006) - Integração com Chatwoot
- **copilotkit.api** (Porta 8081) - Backend para CopilotKit
- **webchat.api** - API para WebChat

### Workers (Background Services)

- **chatwoot.worker** - Tarefas agendadas Chatwoot
- **aocubo.worker** - Integração Aocubo

### Test Clients (Frontend/Testing)

- **copilotkit.test-client** (Porta 5173) - Cliente React CopilotKit
- **webchat.test-client** (Porta 3001) - Cliente teste WebChat
- **agui.test-server** (Porta 4013) - Servidor teste AG-UI

### Bibliotecas Compartilhadas

- **@lisa/common** - Utilitários e tipos compartilhados
- **@lisa/crm-sdk** - SDK para integração CRM
- **@lisa/twenty-sdk** - SDK auto-gerado Twenty
- **@lisa/lead-workflow** - Workflows de leads
- **@lisa/main-workflow** - Workflows principais

## 🛠️ Comandos Principais

```bash
# Setup inicial
pnpm install && pnpm build

# Executar todos os serviços
pnpm dev

# Executar serviços específicos
pnpm dev:ai.api          # API principal
pnpm dev:copilotkit.api  # CopilotKit
pnpm dev:ai.test         # Teste de workflows

# Qualidade de código
pnpm lint && pnpm format:check

# Build para produção
pnpm build
```

## 📞 Suporte

Se você não encontrar a informação que precisa nesta documentação:

1. **Verifique primeiro:** [Troubleshooting](./setup/TROUBLESHOOTING.md)
2. **Para problemas de setup:** [Guia de Início Rápido](./setup/QUICK_START_GUIDE.md)
3. **Para problemas de desenvolvimento:** [Workflow de Desenvolvimento](./development/DEVELOPMENT_WORKFLOW.md)

---

**Última atualização:** Junho 2025  
**Versão da documentação:** 2.0 (Reorganizada)
