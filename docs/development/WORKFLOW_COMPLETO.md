# 🚀 Workflow Completo - Do Desenvolvimento ao Deploy

Este documento descreve o processo completo desde a modificação de código até o deploy automático das imagens Docker no projeto Lisa.

## 📋 Visão Geral do Processo

```mermaid
graph TD
    A[Modificação de Código] --> B[Criar Branch Feature]
    B --> C[Fazer Alterações]
    C --> D[Commit & Push]
    D --> E[Criar Pull Request]
    E --> F[Code Review]
    F --> G[Merge para Main]
    G --> H[Atualizar Versão]
    H --> I[Criar Release PR]
    I --> J[Merge Release PR]
    J --> K[Criar Git Tag]
    K --> L[Pipeline Automática]
    L --> M[Build Docker Image]
    M --> N[Push para Registry]
```

## 🔄 Processo Detalhado

### 1. 📝 Desenvolvimento

#### 1.1 Criar Branch Feature

```bash
# Partir da branch main atualizada
git checkout main
git pull origin main

# Criar nova branch para feature
git checkout -b feature/nova-funcionalidade
```

#### 1.2 Fazer Alterações

```bash
# Fazer modificações no código
cd apps/ai.api
# ... editar arquivos ...

# Testar localmente
pnpm dev:ai.api
```

#### 1.3 Commit e Push

```bash
# Adicionar arquivos modificados
git add .

# Commit com mensagem descritiva
git commit -m "feat: adicionar nova funcionalidade X"

# Push da branch
git push origin feature/nova-funcionalidade
```

### 2. 🔍 Pull Request

#### 2.1 Criar Pull Request no Bitbucket

1. Acesse o repositório no Bitbucket
2. Clique em "Create pull request"
3. Selecione:
   - **Source**: `feature/nova-funcionalidade`
   - **Destination**: `main`
4. Preencha:
   - **Título**: Descrição clara da mudança
   - **Descrição**: Detalhes técnicos e contexto
   - **Reviewers**: Adicione revisores necessários

#### 2.2 Template de Pull Request

```markdown
## 📝 Descrição

Breve descrição das mudanças implementadas.

## 🎯 Tipo de Mudança

- [ ] Bug fix
- [ ] Nova feature
- [ ] Breaking change
- [ ] Documentação

## ✅ Checklist

- [ ] Código testado localmente
- [ ] Testes passando
- [ ] Documentação atualizada
- [ ] Sem conflitos com main

## 🧪 Como Testar

1. Fazer checkout da branch
2. Executar `pnpm install`
3. Executar `pnpm dev:ai.api`
4. Testar funcionalidade X
```

#### 2.3 Code Review

- Aguardar aprovação dos revisores
- Fazer ajustes se necessário
- Resolver comentários

### 3. 🔀 Merge e Release

#### 3.1 Merge para Main

```bash
# Após aprovação, fazer merge no Bitbucket
# Ou via linha de comando:
git checkout main
git pull origin main
git merge feature/nova-funcionalidade
git push origin main
```

#### 3.2 Atualizar Versão

```bash
# Editar package.json do projeto
cd apps/ai.api
# Atualizar campo "version": "0.0.2"

# Commit da nova versão
git add package.json
git commit -m "chore: bump version to 0.0.2"
git push origin main
```

#### 3.3 Criar Release PR (Opcional)

Para mudanças importantes, criar PR de release:

```bash
git checkout -b release/ai.api-0.0.2
# Fazer ajustes finais se necessário
git push origin release/ai.api-0.0.2
# Criar PR: release/ai.api-0.0.2 → main
```

### 4. 🏷️ Criação de Tag e Deploy

#### 4.1 Criar Git Tag

```bash
# Após merge, criar tag seguindo padrão Mastra
git tag @lisa/ai.api@0.0.2
git push origin @lisa/ai.api@0.0.2
```

#### 4.2 Pipeline Automática

A tag dispara automaticamente a pipeline do Bitbucket:

```yaml
# bitbucket-pipelines.yml
pipelines:
  tags:
    '@lisa/ai.api@*':
      - step:
          name: Build and Push AI API Image
          script:
            - ./scripts/docker-build.sh $BITBUCKET_TAG apps/ai.api push
```

#### 4.3 Processo de Build

1. **Validação**: Script valida se tag corresponde ao package.json
2. **Build**: Constrói imagem Docker
3. **Push**: Envia para registry configurado

## 🧪 Testando o Processo

### Teste de Validação de Tags

```bash
# Testar validação com cenários de sucesso e erro
pnpm test:tag-validation

# Testar apenas cenários de erro
pnpm test:tag-validation:error
```

### Build Local

```bash
# Testar build local antes do deploy
pnpm docker:build:ai.api

# Com push para registry (se configurado)
./scripts/docker-build.sh @lisa/ai.api@0.0.2 apps/ai.api push
```

## ⚠️ Troubleshooting

### Erro de Validação de Tag

```
❌ Tag validation failed!
   Expected: @lisa/ai.api@0.0.1
   Got: @lisa/ai.api@0.0.2
```

**Solução**: Atualizar versão no package.json ou criar tag correta.

### Pipeline Não Disparou

**Verificar**:

1. Tag foi criada corretamente?
2. Tag segue padrão `@lisa/projeto@versão`?
3. Pipeline está configurada no bitbucket-pipelines.yml?

### Build Falhou

**Verificar**:

1. Dockerfile existe no projeto?
2. Dependências estão corretas?
3. Variáveis de ambiente configuradas?

## 📚 Comandos Úteis

```bash
# Desenvolvimento
pnpm dev:ai.api              # Executar em modo desenvolvimento
pnpm build:ai.api            # Build do projeto

# Docker
pnpm docker:build:ai.api     # Build da imagem Docker
./scripts/docker-build.sh @lisa/ai.api@0.0.1 apps/ai.api push

# Git
git tag -l "@lisa/ai.api@*"  # Listar tags do projeto
git tag -d @lisa/ai.api@0.0.1 # Deletar tag local
git push origin :refs/tags/@lisa/ai.api@0.0.1 # Deletar tag remota

# Testes
pnpm test:tag-validation     # Testar validação de tags
```

## 🎯 Boas Práticas

1. **Sempre testar localmente** antes de criar PR
2. **Usar mensagens de commit descritivas** seguindo padrão conventional commits
3. **Atualizar versão** antes de criar tag
4. **Documentar mudanças** no PR
5. **Fazer code review** cuidadoso
6. **Testar pipeline** com tags de teste se necessário

## 📈 Próximos Passos

- [ ] Implementar testes automatizados na pipeline
- [ ] Adicionar notificações de deploy
- [ ] Configurar ambientes de staging
- [ ] Implementar rollback automático
