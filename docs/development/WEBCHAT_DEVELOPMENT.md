# WebChat Development Guide

Este guia explica como desenvolver e testar o sistema WebChat do Lisa usando o `webchat.api` e o `webchat.test-client`.

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌──────────────────┐
│  WebChat Client │    │  WebChat API    │    │  Orchestrator    │
│  (Browser)      │◄──►│  (Node.js)      │◄──►│  Worker          │
│  HTML + JS      │    │  WebSocket      │    │  (Node.js)       │
└─────────────────┘    └─────────────────┘    └──────────────────┘
                                │                       │
                                ▼                       ▼
                        ┌──────────────────────────────────────┐
                        │         Apache Kafka                 │
                        │       Message Broker                 │
                        └──────────────────────────────────────┘
```

### **Componentes**

- **WebChat Client**: Interface web para teste (HTML + JavaScript + WebSocket)
- **WebChat API**: Recebe conexões WebSocket e processa mensagens
- **Orchestrator Worker**: Roteia mensagens entre diferentes workers
- **Apache Kafka**: Message broker para comunicação entre workers

### **Fluxo Atual**

1. **Browser** conecta via WebSocket ao **WebChat Worker**
2. **WebChat Worker** recebe mensagem do browser
3. **WebChat Worker** envia para **Orchestrator** via Kafka
4. **Orchestrator** processa e roteia para outros workers se necessário
5. **Orchestrator** responde via Kafka para **WebChat Worker**
6. **WebChat Worker** envia resposta via WebSocket para **Browser**

### **Fluxo Futuro (Direto)**

1. **Browser** conecta diretamente ao **WebChat Worker**
2. **WebChat Worker** processa mensagem localmente
3. **Resposta imediata** via WebSocket (sem Kafka para casos simples)

## 🚀 Quick Start

### **Opção 1: Script Automático (Recomendado)**

```bash
# Linux/Mac
npm run dev:webchat

# Windows
scripts/dev-webchat.bat
```

### **Opção 2: Manual**

```bash
# Terminal 1: Orchestrator
npm run start:dev

# Terminal 2: WebChat API
npm run dev:webchat.api

# Terminal 3: Test Client
npm run start:webchat.test-client:dev

# Abrir no navegador
open http://localhost:3001
```

## 🧪 Testando

### **1. Teste Básico**

1. Abra `http://localhost:3001`
2. Selecione ambiente "Local"
3. Digite uma mensagem
4. Verifique se a mensagem aparece no chat

### **2. Teste com Debug**

1. Clique no botão "Debug" no header
2. Envie mensagens e observe os logs
3. Verifique eventos WebSocket no painel debug

### **3. Teste de Reconexão**

1. Pare o webchat.worker
2. Observe status "Desconectado"
3. Reinicie o serviço
4. Clique em "Reconectar" ou mude ambiente

## 🔧 Desenvolvimento

### **Modificando o WebChat Worker**

```typescript
// apps/webchat.worker/src/webchat.worker.service.ts
async handleInput(input: WebchatInputMessageDto): Promise<void> {
  // Sua lógica personalizada aqui
  console.log('Processing WebChat message:', input);

  // Exemplo: Integração com IA
  const response = await this.processWithAI(input.text);

  // Enviar resposta
  await this.sendResponse(input.socketId, response);
}
```

### **Modificando o Test Client**

```javascript
// apps/webchat.test-client/public/webchat-client.js
// Adicionar novos endpoints
this.endpoints = {
  custom: 'ws://localhost:3002/webchat',
  // ... outros endpoints
};
```

### **Adicionando Novos Ambientes**

1. Edite `webchat-client.js` para adicionar endpoint
2. Atualize o select no `index.hbs`
3. Configure o serviço correspondente

## 📡 Integração com WebChat Worker

### **Endpoint WebSocket Planejado**

```
ws://localhost:3002/webchat?sessionId={uuid}
```

### **Fluxo de Mensagens**

1. Cliente envia mensagem via WebSocket
2. WebChat Worker recebe diretamente
3. Processa mensagem
4. Envia resposta via WebSocket
5. Cliente recebe resposta

## 🔮 Próximas Funcionalidades

### **WebSocket Gateway Direto**

```typescript
// Implementação futura: WebSocket Gateway no webchat.worker
@WebSocketGateway(3002, { path: '/webchat' })
export class WebchatGateway {
  @SubscribeMessage('message')
  handleMessage(client: Socket, payload: any): void {
    // Processar mensagem diretamente
  }
}
```

## 🛠️ Troubleshooting

### **Problema: Test Client não conecta**

- Verifique se o webchat.worker está rodando na porta 3002
- Confirme se o WebSocket endpoint está ativo
- Verifique logs no console do navegador

### **Problema: Mensagens não chegam ao Worker**

- Verifique se Kafka está rodando
- Confirme se Orchestrator está ativo
- Verifique logs do Orchestrator Worker

### **Problema: Worker não responde**

- Verifique logs do WebChat Worker
- Confirme se está consumindo do tópico correto
- Teste publisher manualmente

## 📊 Monitoramento

### **Logs Importantes**

```bash
# Orchestrator Worker
[Orchestrator] Routing to WebChat worker

# WebChat Worker
[WebChat] Processing message: {text}

# Test Client (Browser Console)
[WebSocket] Message received: {data}
```

### **Métricas**

- Latência de mensagens
- Taxa de reconexão
- Throughput de mensagens
- Erros de WebSocket

## 🎯 Próximos Passos

1. **WebSocket Gateway**: Implementar endpoint WebSocket direto no Node.js
2. **Persistência**: Adicionar banco de dados para histórico
3. **Autenticação**: Implementar auth para sessões
4. **Scaling**: Preparar para múltiplas instâncias
5. **Monitoring**: Adicionar métricas e alertas

## 📚 Recursos

- [NestJS WebSockets](https://docs.nestjs.com/websockets/gateways)
- [Kafka.js Documentation](https://kafka.js.org/)
- [WebSocket API](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)
- [Express Static Files](https://expressjs.com/en/starter/static-files.html)
