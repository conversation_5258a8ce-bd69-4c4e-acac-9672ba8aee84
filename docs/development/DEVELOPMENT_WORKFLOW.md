# 🚀 Workflow de Desenvolvimento - Projeto Lisa

Este documento descreve o workflow rigoroso de desenvolvimento implementado no projeto Lisa para garantir qualidade de código e consistência.

## 📋 Pré-requisitos

### Obrigatórios

- **VSCode** - Editor obrigatório para todos os desenvolvedores
- **pnpm** - Gerenciador de pacotes (versão >= 10.12.1)
- **Node.js** - Versão 22+ (recomendado via nvm)
- **nvm** - Para gerenciamento de versões do Node.js (opcional, mas recomendado)

### Extensões VSCode Obrigatórias

As seguintes extensões serão automaticamente sugeridas pelo VSCode:

- **Prettier** (`esbenp.prettier-vscode`) - Formatação de código
- **ESLint** (`dbaeumer.vscode-eslint`) - Linting e qualidade de código
- **EditorConfig** (`editorconfig.editorconfig`) - Configurações básicas do editor
- **Tailwind CSS** (`bradlc.vscode-tailwindcss`) - Suporte ao Tailwind
- **TypeScript** (`ms-vscode.vscode-typescript-next`) - Suporte avançado ao TypeScript

## 🔧 Configuração Inicial

1. **Clone o repositório**
2. **Instale as dependências:**
   ```bash
   pnpm install
   ```
3. **Configure o Husky (git hooks):**
   ```bash
   pnpm prepare
   ```

### 🔧 Suporte Automático ao NVM

O projeto possui suporte automático ao **nvm** nos git hooks. Se você usa nvm para gerenciar versões do Node.js:

1. **Arquivo `.nvmrc`** - Define a versão Node.js 22 para o projeto
2. **Git hooks automáticos** - Carregam o nvm automaticamente se Node.js não estiver no PATH
3. **Detecção inteligente** - Funciona com instalações do nvm em:
   - `~/.nvm/nvm.sh` (instalação padrão)
   - `/usr/local/share/nvm/nvm.sh` (instalação global)
   - `/opt/homebrew/opt/nvm/nvm.sh` (Homebrew no macOS)
   - `~/.config/nvm/nvm.sh` (configuração personalizada)

**Não precisa fazer nada extra** - os hooks detectam e configuram automaticamente!

## 📝 Scripts Disponíveis

### Scripts de Desenvolvimento

```bash
# Inicia o desenvolvimento (com validação prévia)
pnpm dev

# Validação manual antes do desenvolvimento
pnpm pre-dev

# Scripts específicos de apps
pnpm dev:ai.api
pnpm dev:webchat
pnpm dev:agui.test-client
# ... outros apps
```

### Scripts de Qualidade

```bash
# Formatação
pnpm format:check    # Verifica formatação
pnpm format:write    # Aplica formatação

# Linting
pnpm lint           # Executa linting
pnpm lint:fix       # Corrige problemas automaticamente

# Type checking
pnpm type-check     # Verifica tipos TypeScript

# Validação completa
pnpm pre-dev        # Executa todas as validações
```

## 🛡️ Validações Automáticas

### Pre-commit (Git Hooks)

Antes de cada commit, o sistema executa automaticamente:

1. **Type checking** - Verifica erros de TypeScript
2. **Linting** - Aplica correções ESLint
3. **Formatação** - Formata código com Prettier

### Pre-development

Antes de iniciar o desenvolvimento (`pnpm dev`), o sistema executa:

1. **Verificação de formatação** - Garante que o código está formatado
2. **Linting** - Verifica qualidade do código
3. **Type checking** - Valida tipos TypeScript

## 🚫 Regras Rigorosas

### ESLint - Regras Ativas

- `@typescript-eslint/no-explicit-any: error` - Proíbe uso de `any`
- `@typescript-eslint/no-unused-vars: error` - Proíbe variáveis não utilizadas
- `@typescript-eslint/no-floating-promises: error` - Exige tratamento de Promises
- `prefer-const: error` - Força uso de `const` quando possível
- `no-console: warn` - Avisa sobre console.log
- `no-debugger: error` - Proíbe debugger statements

### Prettier - Configuração

- **Print Width:** 120 caracteres
- **Tab Width:** 2 espaços
- **Single Quotes:** Sim
- **Trailing Commas:** Sempre
- **Semicolons:** Sempre

### Commit Messages

- **Mínimo:** 10 caracteres
- **Formato:** Deve começar com letra maiúscula
- **Validação:** Automática via git hook

## 🔄 Fluxo de Trabalho

### 1. Antes de Desenvolver

```bash
# O sistema executa automaticamente, mas você pode rodar manualmente:
pnpm pre-dev
```

### 2. Durante o Desenvolvimento

- O VSCode formata automaticamente ao salvar
- ESLint mostra erros em tempo real
- TypeScript valida tipos continuamente

### 3. Antes do Commit

```bash
# Opcional - corrigir problemas manualmente
pnpm lint:fix
pnpm format:write

# O git hook executa automaticamente:
git add .
git commit -m "Sua mensagem de commit"
```

### 4. Se Houver Problemas

O sistema **bloqueia** commits e desenvolvimento se houver:

- Erros de TypeScript
- Problemas de linting não corrigíveis
- Código mal formatado
- Mensagens de commit inválidas

## 🆘 Resolução de Problemas

### Erro de Formatação

```bash
pnpm format:write
```

### Erros de Linting

```bash
pnpm lint:fix
```

### Erros de TypeScript

- Corrija manualmente os erros mostrados
- Execute `pnpm type-check` para verificar

### Git Hooks Não Funcionam

```bash
pnpm prepare
chmod +x .husky/pre-commit
chmod +x .husky/commit-msg
```

## 🎯 Benefícios

- **Qualidade Garantida:** Código sempre formatado e sem erros
- **Consistência:** Todos os desenvolvedores seguem as mesmas regras
- **Produtividade:** Problemas detectados antes do commit
- **Manutenibilidade:** Código limpo e bem estruturado
- **CI/CD Confiável:** Builds sempre passam

## 📞 Suporte

Em caso de dúvidas ou problemas:

1. Verifique este documento
2. Execute `pnpm pre-dev` para diagnóstico
3. Consulte a equipe de desenvolvimento

---

**⚠️ IMPORTANTE:** Este workflow é obrigatório para todos os desenvolvedores. Não é possível fazer commits ou iniciar desenvolvimento sem passar pelas validações.
