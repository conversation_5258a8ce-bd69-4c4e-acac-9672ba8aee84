# 🚀 Guia de CI/CD - Projeto Lisa

Este documento descreve como usar o sistema de CI/CD implementado no projeto Lisa para fazer deploy automático das APIs usando Bitbucket Pipelines.

## 📋 Visão Geral

O sistema de CI/CD está configurado para fazer build e push automático de imagens Docker para os seguintes projetos:

- **ai.api** - API baseada em Mastra para workflows e agentes de IA
- **twilio.api** - API para serviços de comunicação SMS e voz
- **smarters.api** - API de integração com Chatwoot e serviços de mensagens

## 🏷️ Padrão de Tags

O sistema segue o padrão de tags do Mastra:

```
{package-name}@{version}
```

### Exemplos:

- `@lisa/ai.api@0.0.1`
- `@lisa/twilio.api@0.0.1`
- `@lisa/smarters.api@0.0.1`

## 🔄 Como Fazer Deploy

### 1. Preparar o Código

1. Faça suas alterações no código
2. Teste localmente usando os scripts de desenvolvimento:
   ```bash
   pnpm dev:ai.api      # Para ai.api
   pnpm dev:twilio.api  # Para twilio.api
   pnpm dev:smarters.api # Para smarters.api
   ```

### 2. Atualizar a Versão

1. Navegue até o diretório do projeto:

   ```bash
   cd apps/ai.api  # ou apps/twilio.api ou apps/smarters.api
   ```

2. Atualize a versão no `package.json`:
   ```json
   {
     "name": "@lisa/ai.api",
     "version": "0.0.2",  // Incremente a versão
     ...
   }
   ```

### 3. Criar e Enviar a Tag

1. Commit suas alterações:

   ```bash
   git add .
   git commit -m "feat: nova funcionalidade para ai.api"
   ```

2. Criar a tag seguindo o padrão:

   ```bash
   git tag @lisa/ai.api@0.0.2
   ```

3. Enviar para o repositório:
   ```bash
   git push origin main
   git push origin @lisa/ai.api@0.0.2
   ```

### 4. Acompanhar o Deploy

1. Acesse o Bitbucket Pipelines
2. Verifique se o pipeline foi acionado pela tag
3. Acompanhe o progresso do build e deploy

## 🔍 Script de Build Docker

O sistema inclui um script unificado `scripts/docker-build.sh` que:

1. **Valida a tag**: Confirma se a tag corresponde ao nome e versão do `package.json`
2. **Extrai informações**: Automaticamente extrai o nome do projeto e versão da tag
3. **Build multi-stage**: Usa Dockerfiles com build interno (sem dependência externa)
4. **Push opcional**: Pode fazer push para registry se solicitado
5. **Consistência**: Mesmo processo usado localmente e no pipeline

### Vantagens do Novo Approach:

- ✅ **Build interno**: Todo o processo de build acontece dentro do Docker
- ✅ **Testável localmente**: Mesmo comando funciona local e no pipeline
- ✅ **Validação integrada**: Tag validation automática antes do build
- ✅ **Multi-stage**: Imagens otimizadas para produção
- ✅ **Health checks**: Verificações de saúde incluídas nas imagens

## 🐳 Imagens Docker

As imagens são criadas com os seguintes nomes:

```
{CR_HOSTNAME}/lisa-workspace-{project-name}:{version}
```

### Exemplos:

- `registry.com/lisa-workspace-ai.api:0.0.1`
- `registry.com/lisa-workspace-twilio.api:0.0.1`
- `registry.com/lisa-workspace-smarters.api:0.0.1`

## 🛠️ Scripts Disponíveis

### Build Local

#### Build de Código (TypeScript/JavaScript)

```bash
pnpm build:ai.api      # Build apenas ai.api
pnpm build:twilio.api  # Build apenas twilio.api
pnpm build:smarters.api # Build apenas smarters.api
pnpm build             # Build todos os projetos
```

#### Build de Imagem Docker

```bash
# Build local (sem push)
./scripts/docker-build.sh @lisa/ai.api@0.0.1 apps/ai.api

# Build e push para registry
./scripts/docker-build.sh @lisa/ai.api@0.0.1 apps/ai.api push

# Exemplos para outros projetos
./scripts/docker-build.sh @lisa/twilio.api@0.0.1 apps/twilio.api
./scripts/docker-build.sh @lisa/smarters.api@0.0.1 apps/smarters.api push
```

### Desenvolvimento

```bash
pnpm dev:ai.api        # Desenvolvimento ai.api
pnpm dev:twilio.api    # Desenvolvimento twilio.api
pnpm dev:smarters.api  # Desenvolvimento smarters.api
```

## ⚠️ Troubleshooting

### Tag não corresponde ao package.json

```
❌ Tag validation failed!
   Expected: @lisa/ai.api@0.0.2
   Got: @lisa/ai.api@0.0.1
```

**Solução**: Atualize a versão no `package.json` ou crie uma nova tag com a versão correta.

### Projeto não encontrado

```
❌ Project directory not found: apps/ai.api
```

**Solução**: Verifique se o nome do projeto na tag está correto e se o diretório existe.

### Build falhou

1. Verifique os logs do pipeline no Bitbucket
2. Teste o build localmente: `pnpm build:ai.api`
3. Verifique se todas as dependências estão instaladas

## 📝 Exemplo Completo

```bash
# 1. Fazer alterações no código
cd apps/ai.api
# ... fazer alterações ...

# 2. Atualizar versão no package.json
# Editar package.json: "version": "0.0.2"

# 3. Testar build localmente (opcional mas recomendado)
cd ../..  # voltar para raiz
./scripts/docker-build.sh @lisa/ai.api@0.0.2 apps/ai.api

# 4. Commit e tag
git add .
git commit -m "feat: adicionar nova funcionalidade"
git tag @lisa/ai.api@0.0.2

# 5. Push
git push origin main
git push origin @lisa/ai.api@0.0.2

# 6. Acompanhar no Bitbucket Pipelines
# O pipeline usará o mesmo script: ./scripts/docker-build.sh
```

## 🔧 Configuração Técnica

### Bitbucket Pipelines

- **Trigger**: Tags que seguem o padrão `@lisa/*@*`
- **Node.js**: Versão 22 (LTS)
- **Package Manager**: pnpm
- **Docker**: Build e push automático

### Variáveis de Ambiente Necessárias

- `CR_HOSTNAME`: Hostname do container registry
- `CR_USERNAME`: Username do container registry
- `CR_PASSWORD`: Password do container registry

## 📚 Referências

- [Bitbucket Pipelines Documentation](https://support.atlassian.com/bitbucket-cloud/docs/get-started-with-bitbucket-pipelines/)
- [Padrão de Tags do Mastra](https://github.com/mastra-ai/mastra/tags)
- [Docker Build Documentation](https://docs.docker.com/engine/reference/commandline/build/)
