# ⚡ Referência Rápida - Deploy Lisa

G<PERSON> rápido para fazer deploy das aplicações do projeto Lisa.

## 🚀 Deploy em 3 Passos

### 1. Atual<PERSON><PERSON>

```bash
# Editar package.json do projeto
cd apps/ai.api
# Alterar: "version": "0.0.2"

# Commit
git add package.json
git commit -m "chore: bump ai.api to 0.0.2"
git push origin main
```

### 2. Criar e Enviar Tag

```bash
# Voltar para raiz
cd ../..

# Criar tag (pad<PERSON><PERSON>)
git tag @lisa/ai.api@0.0.2

# Enviar tag (dispara pipeline)
git push origin @lisa/ai.api@0.0.2
```

### 3. Monitorar Pipeline

- Acesse Bitbucket → Pipelines
- Procure pela tag `@lisa/ai.api@0.0.2`
- Aguarde conclusão do build

## 🧪 Testar Antes do Deploy

```bash
# Testar validação de todos os projetos
pnpm test:tag-validation

# Testar apenas cenários de erro (demonstração)
pnpm test:tag-validation:error

# Testar projeto específico
pnpm test:tag-validation:ai.api
pnpm test:tag-validation:twilio.api
pnpm test:tag-validation:smarters.api

# Testar build local
pnpm docker:build:ai.api
```

## 📋 Padrão de Tags

| Projeto      | Tag Exemplo                |
| ------------ | -------------------------- |
| AI API       | `@lisa/ai.api@0.0.1`       |
| Twilio API   | `@lisa/twilio.api@0.0.1`   |
| Smarters API | `@lisa/smarters.api@0.0.1` |

## ⚠️ Erros Comuns

### Tag Validation Failed

```bash
❌ Expected: @lisa/ai.api@0.0.1
   Got: @lisa/ai.api@0.0.2
```

**Solução**: Atualizar versão no package.json ou criar tag correta.

### Pipeline Não Disparou

**Verificar**:

- Tag foi enviada? `git ls-remote --tags origin | grep "@lisa/ai.api"`
- Tag segue padrão? `@lisa/projeto@versão`

## 🛠️ Comandos Úteis

```bash
# Verificar versão atual
node -p "require('./apps/ai.api/package.json').version"

# Listar tags do projeto
git tag -l "@lisa/ai.api@*"

# Deletar tag (se necessário)
git tag -d @lisa/ai.api@0.0.1
git push origin :refs/tags/@lisa/ai.api@0.0.1

# Build manual
./scripts/docker-build.sh @lisa/ai.api@0.0.1 apps/ai.api
```

## 📚 Documentação Completa

- [Workflow Completo](./WORKFLOW_COMPLETO.md) - Processo detalhado
- [Guia de Deploy](./DEPLOY_GUIDE.md) - Deploy e troubleshooting
- [Guia de CI/CD](./CI_CD_GUIDE.md) - Configuração de pipelines

## 🎯 Checklist Rápido

- [ ] Código testado
- [ ] Versão atualizada no package.json
- [ ] Tag criada com padrão correto
- [ ] Tag enviada para Bitbucket
- [ ] Pipeline executada com sucesso
