# 🚀 Guia de Deploy - <PERSON><PERSON>o Lisa

Este documento explica como fazer deploy das aplicações do projeto Lisa usando tags Git e pipelines automáticas do Bitbucket.

## 📋 Pré-requisitos

- Código commitado e testado
- Versão atualizada no `package.json` do projeto
- Acesso ao repositório Bitbucket
- Registry Docker configurado (variáveis de ambiente)

## 🏷️ Sistema de Tags

### Padrão de Tags

O projeto segue o padrão de tags do Mastra:

```
{package-name}@{version}
```

### Exemplos de Tags Válidas

```bash
@lisa/ai.api@0.0.1        # AI API versão 0.0.1
@lisa/twilio.api@0.0.2    # Twilio API versão 0.0.2
@lisa/smarters.api@1.0.0  # Smarters API versão 1.0.0
```

## 🔄 Processo de Deploy

### 1. Preparar Release

#### 1.1 Atualizar Versão

```bash
# Editar package.json do projeto
cd apps/ai.api
# Alterar: "version": "0.0.2"

# Commit da nova versão
git add package.json
git commit -m "chore: bump ai.api version to 0.0.2"
git push origin main
```

#### 1.2 Testar Localmente (Recomendado)

```bash
# Voltar para raiz do projeto
cd ../..

# Testar validação de tag
pnpm test:tag-validation

# Testar build local
pnpm docker:build:ai.api
```

### 2. Criar e Enviar Tag

#### 2.1 Criar Tag Git

```bash
# Criar tag seguindo o padrão
git tag @lisa/ai.api@0.0.2

# Verificar se a tag foi criada
git tag -l "@lisa/ai.api@*"
```

#### 2.2 Enviar Tag para Bitbucket

```bash
# Push da tag (isso dispara a pipeline)
git push origin @lisa/ai.api@0.0.2
```

### 3. Monitorar Pipeline

#### 3.1 Acompanhar no Bitbucket

1. Acesse o repositório no Bitbucket
2. Vá para **Pipelines**
3. Procure pela pipeline da tag `@lisa/ai.api@0.0.2`
4. Monitore o progresso do build

#### 3.2 Logs da Pipeline

A pipeline executa os seguintes passos:

1. **Validação**: Verifica se tag corresponde ao package.json
2. **Build**: Constrói a imagem Docker
3. **Push**: Envia imagem para o registry

## 🧪 Testando Validação de Tags

### Comando de Teste

```bash
# Testar todos os cenários (sucesso e erro)
pnpm test:tag-validation

# Testar apenas cenários de erro
pnpm test:tag-validation:error
```

### Exemplo de Saída - Sucesso

```
✅ Tag validation successful!
   Expected: @lisa/ai.api@0.0.1
   Got: @lisa/ai.api@0.0.1
```

### Exemplo de Saída - Erro

```
❌ Tag validation failed!
   Expected: @lisa/ai.api@0.0.1
   Got: @lisa/ai.api@0.0.2

💡 To fix this:
   1. Update the version in apps/ai.api/package.json to match the tag
   2. Or create a new tag: git tag @lisa/ai.api@0.0.1
```

## ⚠️ Troubleshooting

### Erro: Tag Validation Failed

**Problema**: Tag não corresponde ao package.json

```bash
❌ Tag validation failed!
   Expected: @lisa/ai.api@0.0.1
   Got: @lisa/ai.api@0.0.2
```

**Soluções**:

1. **Atualizar package.json**:

   ```bash
   # Editar apps/ai.api/package.json
   # Alterar "version": "0.0.2"
   git add apps/ai.api/package.json
   git commit -m "chore: bump version to 0.0.2"
   git push origin main
   ```

2. **Criar nova tag correta**:

   ```bash
   # Deletar tag incorreta
   git tag -d @lisa/ai.api@0.0.2
   git push origin :refs/tags/@lisa/ai.api@0.0.2

   # Criar tag correta
   git tag @lisa/ai.api@0.0.1
   git push origin @lisa/ai.api@0.0.1
   ```

### Erro: Pipeline Não Disparou

**Verificar**:

1. Tag foi enviada para o repositório?

   ```bash
   git ls-remote --tags origin | grep "@lisa/ai.api"
   ```

2. Tag segue o padrão correto?

   - ✅ `@lisa/ai.api@0.0.1`
   - ❌ `ai.api-0.0.1`
   - ❌ `@lisa/ai.api-0.0.1`

3. Pipeline está configurada no `bitbucket-pipelines.yml`?

### Erro: Docker Build Failed

**Verificar**:

1. Dockerfile existe no projeto?
2. Dependências estão corretas no package.json?
3. Código compila sem erros?

**Testar localmente**:

```bash
# Build local para debug
./scripts/docker-build.sh @lisa/ai.api@0.0.1 apps/ai.api
```

## 📚 Comandos Úteis

### Gerenciamento de Tags

```bash
# Listar todas as tags do projeto
git tag -l "@lisa/ai.api@*"

# Listar tags remotas
git ls-remote --tags origin | grep "@lisa/ai.api"

# Deletar tag local
git tag -d @lisa/ai.api@0.0.1

# Deletar tag remota
git push origin :refs/tags/@lisa/ai.api@0.0.1

# Ver detalhes de uma tag
git show @lisa/ai.api@0.0.1
```

### Build e Deploy

```bash
# Build local (sem push)
pnpm docker:build:ai.api

# Build com tag específica
./scripts/docker-build.sh @lisa/ai.api@0.0.1 apps/ai.api

# Build e push para registry
./scripts/docker-build.sh @lisa/ai.api@0.0.1 apps/ai.api push
```

### Verificação

```bash
# Verificar versão atual no package.json
node -p "require('./apps/ai.api/package.json').version"

# Verificar nome do package
node -p "require('./apps/ai.api/package.json').name"

# Testar validação específica
node scripts/validate-tag.js @lisa/ai.api@0.0.1 apps/ai.api
```

## 🎯 Checklist de Deploy

- [ ] Código testado e funcionando
- [ ] Versão atualizada no package.json
- [ ] Commit da nova versão feito
- [ ] Tag criada seguindo padrão correto
- [ ] Tag enviada para Bitbucket
- [ ] Pipeline executada com sucesso
- [ ] Imagem Docker disponível no registry
- [ ] Deploy verificado no ambiente de produção

## 📈 Próximos Passos

- [ ] Automatizar bump de versão
- [ ] Implementar rollback automático
- [ ] Adicionar notificações de deploy
- [ ] Configurar ambientes de staging
