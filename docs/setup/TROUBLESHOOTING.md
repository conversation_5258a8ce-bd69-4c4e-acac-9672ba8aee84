# Troubleshooting Guide - <PERSON> WebChat

Este guia ajuda a resolver problemas comuns durante o desenvolvimento do WebChat.

## 🔧 Problemas Comuns

### **1. <PERSON>rro: "Property 'provider' does not exist"**

**Problema:**

```
Property 'provider' does not exist on type 'OrchestratorResponseMessageDto'
```

**Solução:**
✅ **Já corrigido!** O DTO foi atualizado para incluir a propriedade `provider`.

### **2. <PERSON>rro: "KafkaJSProtocolError: Invalid topic"**

**Problema:**

```
KafkaJSProtocolError: The request attempted to perform an operation on an invalid topic
```

**Causa:** Kafka não está rodando ou tópicos não existem.

**Soluções:**

#### **Opção 1: Usar Docker Compose (Recomendado)**

```bash
# Iniciar Kafka
npm run kafka:start

# Aguardar Kafka estar pronto (30-60 segundos)
# Verificar se está rodando
docker ps | grep kafka
```

#### **Opção 2: Verificar manualmente**

```bash
# Verificar se Kafka está rodando
nc -z localhost 9092

# Se não estiver, iniciar manualmente
npm run kafka:start
```

#### **Opção 3: Configurar tópicos manualmente**

```bash
# Executar script de setup
npm run kafka:setup
```

### **3. Erro: "npm: command not found"**

**Problema:**

```bash
bash: npm: command not found
```

**Solução:**

```bash
# Carregar NVM
source ~/.nvm/nvm.sh

# Usar Node.js 22
nvm use 22

# Verificar
node --version
npm --version
```

### **4. Erro: "Port already in use"**

**Problema:**

```
Port 3000/3001 is already in use
```

**Soluções:**

#### **Encontrar processo usando a porta:**

```bash
# Verificar porta 3000
lsof -i :3000

# Verificar porta 3001
lsof -i :3001

# Matar processo
kill -9 <PID>
```

#### **Usar portas diferentes:**

```bash
# Editar main.ts dos apps para usar portas diferentes
# Ou definir variável de ambiente
PORT=3002 npm run start:webchat.test-client:dev
```

### **5. WebChat Test Client não conecta**

**Problema:** Interface carrega mas não conecta ao WebSocket.

**Diagnóstico:**

1. Abrir DevTools (F12)
2. Ir para Console
3. Verificar erros de WebSocket

**Soluções:**

#### **Verificar serviço .NET:**

```bash
# O serviço .NET deve estar rodando em:
# http://localhost:5001
# ws://localhost:5001/v1/webchat/listen

# Verificar se está rodando
curl http://localhost:5001/health
```

#### **Verificar ambiente selecionado:**

- No test client, verificar se ambiente está correto
- "Local (.NET)" para conectar ao serviço .NET
- "Local (Node.js)" para futuro endpoint Node.js

### **6. Mensagens não chegam ao Worker**

**Problema:** Mensagens enviadas não aparecem nos logs do WebChat Worker.

**Diagnóstico:**

```bash
# Verificar logs do Orchestrator
# Deve mostrar: "Routing to WebChat worker"

# Verificar logs do WebChat Worker
# Deve mostrar: "WebChat Worker received input"
```

**Soluções:**

#### **Verificar roteamento:**

1. Verificar se `isWebChatMessage()` está funcionando
2. Verificar se `messageType` está correto
3. Verificar logs do Orchestrator

#### **Verificar tópicos Kafka:**

```bash
# Listar tópicos
docker exec lisa-kafka kafka-topics --list --bootstrap-server localhost:9092

# Verificar mensagens em tópico
docker exec lisa-kafka kafka-console-consumer --topic webchat.input --bootstrap-server localhost:9092 --from-beginning
```

## 🚀 Scripts de Desenvolvimento

### **Início Rápido**

```bash
# Tudo em um comando
npm run dev:webchat
```

### **Passo a Passo**

```bash
# 1. Iniciar Kafka
npm run kafka:start

# 2. Aguardar Kafka (30-60s)
sleep 60

# 3. Iniciar serviços
npm run start:dev                    # Terminal 1
npm run start:webchat.worker:dev     # Terminal 2
npm run start:webchat.test-client:dev # Terminal 3
```

### **Apenas Kafka**

```bash
# Iniciar apenas Kafka
npm run kafka:start

# Parar Kafka
npm run kafka:stop

# Configurar tópicos
npm run kafka:setup
```

## 🔍 Debug e Monitoramento

### **Kafka UI**

Após iniciar Kafka, acesse:

- **URL:** http://localhost:8080
- **Função:** Interface web para monitorar Kafka
- **Uso:** Ver tópicos, mensagens, consumers

### **Logs Importantes**

#### **Orchestrator Worker:**

```
[Orchestrator] Routing to WebChat worker
[Orchestrator] Received response: {...}
```

#### **WebChat Worker:**

```
[WebChat] Processing message: {...}
[WebChat] Processed message successfully
```

#### **Test Client (Browser Console):**

```
[WebSocket] Connected successfully
[WebSocket] Message received: {...}
```

### **Verificações de Saúde**

#### **Kafka:**

```bash
nc -z localhost 9092 && echo "Kafka OK" || echo "Kafka DOWN"
```

#### **Test Client:**

```bash
curl http://localhost:3001/health
```

#### **Orchestrator:**

```bash
# Verificar se está consumindo
docker exec lisa-kafka kafka-consumer-groups --bootstrap-server localhost:9092 --describe --group orchestrator-worker-consumer-server
```

## 📞 Suporte

Se os problemas persistirem:

1. **Verificar logs** de todos os serviços
2. **Reiniciar** todos os containers: `npm run kafka:stop && npm run kafka:start`
3. **Limpar** node_modules: `rm -rf node_modules && npm install`
4. **Verificar** se todas as dependências estão instaladas
5. **Consultar** documentação do NestJS e Kafka.js
