# 🚀 Lisa Workspace - Quick Start Guide

Este guia fornece instruções completas para configurar e executar todos os serviços do Lisa Workspace do zero.

## 📋 Pré-requisitos

- **Node.js**: v22 (LTS) - Use nvm para gerenciar versões
- **pnpm**: v10.12.1+ (gerenciador de pacotes)
- **Git**: Para controle de versão

```bash
# Instalar Node.js v22 via nvm
nvm install 22
nvm use 22

# Instalar pnpm globalmente
npm install -g pnpm@latest
```

## 🧹 Setup Inicial (Limpeza e Instalação)

### 1. Limpar arquivos não versionados

```bash
# Remover todos os arquivos de build e cache
rm -rf node_modules dist .turbo .mastra
find . -name "*.tsbuildinfo" -delete
find . -name "*.db" -delete
find . -name "node_modules" -type d -exec rm -rf {} +
```

### 2. Instalar dependências

```bash
# Instalar todas as dependências do workspace
pnpm install

# Fazer build de todas as bibliotecas
pnpm build
```

## 🏗️ Arquitetura dos Serviços

### APIs (Backend Services)

| Serviço            | Porta | Descrição                       | Comando                   |
| ------------------ | ----- | ------------------------------- | ------------------------- |
| **ai.api**         | 4112  | API principal com Mastra/OpenAI | `pnpm dev:ai.api`         |
| **twilio.api**     | 3005  | Integração com Twilio           | `pnpm dev:twilio.api`     |
| **smarters.api**   | 3006  | Integração com Chatwoot         | `pnpm dev:smarters.api`   |
| **copilotkit.api** | 8081  | Backend para CopilotKit         | `pnpm dev:copilotkit.api` |

### Workers (Background Services)

| Serviço             | Porta | Descrição                  | Comando                    |
| ------------------- | ----- | -------------------------- | -------------------------- |
| **webchat.worker**  | 3002  | WebSocket para chat        | `pnpm dev:webchat.worker`  |
| **chatwoot.worker** | N/A   | Tarefas agendadas Chatwoot | `pnpm dev:chatwoot.worker` |
| **aocubo.worker**   | N/A   | Integração Aocubo          | `pnpm dev:aocubo.worker`   |

### Test Clients (Frontend/Testing)

| Serviço                    | Porta | Descrição                | Comando                           |
| -------------------------- | ----- | ------------------------ | --------------------------------- |
| **copilotkit.test-client** | 5173  | Cliente React CopilotKit | `pnpm dev:copilotkit.test-client` |
| **webchat.test-client**    | 3001  | Cliente teste WebChat    | `pnpm dev:webchat.test-client`    |
| **agui.test-client**       | 4013  | Cliente teste AG-UI      | `pnpm dev:agui.test-client`       |

### Test Scripts

| Serviço     | Descrição                 | Comando            |
| ----------- | ------------------------- | ------------------ |
| **ai.test** | Script teste workflows AI | `pnpm dev:ai.test` |

## 🔧 Configuração de Ambiente

### Arquivos .env necessários

Cada serviço precisa de seu arquivo `.env`. Use os `.env.example` como base:

```bash
# Copiar arquivos de exemplo
cp apps/ai.api/.env.example apps/ai.api/.env
cp apps/copilotkit.api/.env.example apps/copilotkit.api/.env
cp apps/agui.test-client/.env.example apps/agui.test-client/.env
```

### Variáveis principais

**ai.api (.env):**

```env
OPENAI_API_KEY=your_openai_key_here
```

**copilotkit.api (.env):**

```env
COPILOT_KIT_PORT_NUMBER=8081
COPILOT_KIT_CLIENT_ORIGIN_URL=http://localhost:5173
COPILOT_KIT_RUNTIME_URL=http://localhost:8000/copilotkit
OPENAI_API_KEY=your_openai_key_here
```

**agui.test-client (.env):**

```env
PORT=4013
AI_API_URL=http://localhost:4112
CORS_ORIGIN=http://localhost:5173
```

## 🚀 Executando os Serviços

### Opção 1: Executar todos os serviços

```bash
# Executar todos em modo desenvolvimento
pnpm dev
```

### Opção 2: Executar serviços individuais

⚠️ **IMPORTANTE**: Se você usar nvm, execute os comandos com `source ~/.nvm/nvm.sh &&` antes:

```bash
# APIs principais
source ~/.nvm/nvm.sh && pnpm dev:ai.api          # Porta 4112
pnpm dev:twilio.api      # Porta 3005
pnpm dev:smarters.api    # Porta 3006
pnpm dev:copilotkit.api  # Porta 8081

# Workers
pnpm dev:webchat.worker   # Porta 3002
pnpm dev:chatwoot.worker  # Background
pnpm dev:aocubo.worker    # Background

# Test Clients
pnpm dev:copilotkit.test-client  # Porta 5173
pnpm dev:webchat.test-client     # Porta 3001
pnpm dev:agui.test-client        # Porta 4013

# Test Scripts
pnpm dev:ai.test         # Script de teste
```

### Opção 3: WebChat Development (Script especial)

```bash
# Script que inicia múltiplos serviços para WebChat
pnpm dev:webchat
```

## 🔍 Verificação de Saúde

### Verificar se os serviços estão rodando

```bash
# Verificar portas em uso
lsof -i :4112  # ai.api
lsof -i :3005  # twilio.api
lsof -i :3006  # smarters.api
lsof -i :8081  # copilotkit.api
lsof -i :5173  # copilotkit.test-client
lsof -i :3001  # webchat.test-client
lsof -i :3002  # webchat.worker
lsof -i :4013  # agui.test-client
```

### URLs de acesso

- **AI API**: http://localhost:4112
- **Twilio API**: http://localhost:3005
- **Smarters API**: http://localhost:3006
- **CopilotKit API**: http://localhost:8081
- **CopilotKit Test Client**: http://localhost:5173
- **WebChat Test Client**: http://localhost:3001
- **AG-UI Test Client**: http://localhost:4013
- **WebChat Worker**: ws://localhost:3002

## 🛠️ Comandos Úteis

### Build e Deploy

```bash
# Build específico
pnpm build:ai.api
pnpm build:twilio.api
pnpm build:smarters.api

# Docker builds
pnpm docker:build:ai.api
pnpm docker:build:twilio.api
pnpm docker:build:smarters.api
```

### Qualidade de Código

```bash
# Linting
pnpm lint
pnpm lint:fix

# Formatação
pnpm format:check
pnpm format:write

# Type checking
pnpm type-check
```

### Testes

```bash
# Executar todos os testes
pnpm test

# Teste específico do AI
pnpm ai  # Alias para pnpm dev:ai.test
```

## 📚 Bibliotecas Compartilhadas

| Biblioteca              | Descrição                          |
| ----------------------- | ---------------------------------- |
| **@lisa/common**        | Utilitários e tipos compartilhados |
| **@lisa/crm-sdk**       | SDK para integração CRM            |
| **@lisa/twenty-sdk**    | SDK auto-gerado Twenty             |
| **@lisa/lead-workflow** | Workflows de leads                 |
| **@lisa/main-workflow** | Workflows principais               |

## 🔧 Troubleshooting

### Problemas comuns

1. **Porta em uso**: Use `lsof -i :PORTA` e `kill -9 PID`
2. **Dependências**: Execute `pnpm install` novamente
3. **Build falhou**: Execute `pnpm clean && pnpm install && pnpm build`
4. **AI API não responde**: Verifique se OPENAI_API_KEY está configurada

### Logs e Debug

```bash
# Ver logs detalhados
DEBUG=* pnpm dev:ai.api

# Verificar health do ai.api
curl http://localhost:4111/api/workflows
```

## 📝 Próximos Passos

1. Configure suas chaves de API nos arquivos `.env`
2. Execute `pnpm dev` para iniciar todos os serviços
3. Acesse os test clients para verificar funcionamento
4. Execute `pnpm ai` para testar workflows AI

Para mais detalhes, consulte:

- `TROUBLESHOOTING.md` - Guia de resolução de problemas
- `docs/` - Documentação técnica detalhada
