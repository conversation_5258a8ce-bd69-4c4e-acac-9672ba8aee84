import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/index.ts'],
  format: ['esm'],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  outDir: 'dist',
  target: 'es2022',
  minify: false,
  bundle: false,
  external: [
    '@lisa/common',
    '@lisa/crm-sdk',
    '@ai-sdk/openai',
    '@mastra/core',
    '@mastra/libsql',
    '@mastra/memory',
    'zod',
  ],
  tsconfig: './tsconfig.lib.json',
});
