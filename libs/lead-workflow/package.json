{"name": "@lisa/lead-workflow", "version": "0.0.1", "description": "Lead workflow management with Mastra integration", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "scripts": {"build": "tsup", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@lisa/common": "workspace:*", "@lisa/crm-sdk": "workspace:*", "@mastra/core": "^0.10.6", "@mastra/libsql": "^0.10.0", "@mastra/memory": "^0.10.1", "zod": "^3.25.41"}, "devDependencies": {"@types/node": "^22.10.7", "jest": "^30.0.2", "ts-jest": "^29.2.5", "tsup": "^8.5.0", "typescript": "^5.8.3"}}