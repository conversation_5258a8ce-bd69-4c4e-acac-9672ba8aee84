{"name": "@lisa/twenty-sdk", "version": "0.0.1", "description": "Twenty CRM SDK integration", "private": true, "type": "module", "main": "src/esm/index.mjs", "types": "src/esm/index.d.mts", "exports": {".": {"import": {"types": "./src/esm/index.d.mts", "default": "./src/esm/index.mjs"}, "require": {"types": "./src/esm/index.d.mts", "default": "./src/esm/index.mjs"}}}, "scripts": {"build": "echo 'SDK is auto-generated, no build needed'", "generate": "cd fern && fern generate --local --log-level debug", "generate:clean": "pnpm generate && rm -rf src/cjs", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"axios": "^1.9.0", "js-base64": "^3.7.7", "qs": "^6.13.1", "url-join": "^5.0.0"}, "devDependencies": {"@types/node": "^22.10.7", "fern-api": "^0.64.15", "jest": "^30.0.2", "ts-jest": "^29.2.5", "typescript": "^5.8.3"}}