{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping - relative to project root */
    "baseUrl": ".",
    "paths": {
      "@lisa/common": ["../../libs/common/src"],
      "@lisa/common/*": ["../../libs/common/src/*"],
      "@lisa/crm-sdk": ["../../libs/crm-sdk/src"],
      "@lisa/crm-sdk/*": ["../../libs/crm-sdk/src/*"],
      "@lisa/lead-workflow": ["../../libs/lead-workflow/src"],
      "@lisa/lead-workflow/*": ["../../libs/lead-workflow/src/*"],
      "@lisa/main-workflow": ["../../libs/main-workflow/src"],
      "@lisa/main-workflow/*": ["../../libs/main-workflow/src/*"],
      "@lisa/twenty-sdk": ["../../libs/twenty-sdk/src"],
      "@lisa/twenty-sdk/*": ["../../libs/twenty-sdk/src/*"],
      "@lisa/twenty-sdk/cjs": ["../../libs/twenty-sdk/src/cjs"],
      "@lisa/twenty-sdk/cjs/*": ["../../libs/twenty-sdk/src/cjs/*"],
      "@lisa/twenty-sdk/cjs/api": ["../../libs/twenty-sdk/src/cjs/api"]
    }
  }
}
