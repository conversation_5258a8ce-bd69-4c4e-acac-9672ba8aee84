#!/bin/bash
# Script: test-general.sh
# Descrição: Teste geral completo do Lisa Workspace
# Uso: bash scripts/test-general.sh

set -e  # Parar em caso de erro

echo "🧪 Iniciando Teste Geral do Lisa Workspace..."
echo "============================================="

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ] || [ ! -f "pnpm-workspace.yaml" ]; then
    echo "❌ Erro: Execute este script no diretório raiz do workspace"
    echo "   Certifique-se de estar em lisa.workspace/"
    exit 1
fi

# Verificar se pnpm está instalado
if ! command -v pnpm &> /dev/null; then
    echo "❌ Erro: pnpm não está instalado"
    echo "   Instale com: npm install -g pnpm@latest"
    exit 1
fi

# Verificar se Docker está disponível (para testes Docker)
DOCKER_AVAILABLE=false
if command -v docker &> /dev/null && docker info &> /dev/null; then
    DOCKER_AVAILABLE=true
    echo "🐳 Docker detectado - testes Docker serão incluídos"
else
    echo "⚠️  Docker não disponível - testes Docker serão pulados"
fi

echo ""
echo "📋 Fase 1: Verificação de Qualidade de Código"
echo "---------------------------------------------"

echo "🔍 Executando lint..."
if pnpm lint; then
    echo "✅ Lint passou"
else
    echo "❌ Lint falhou"
    echo "💡 Tente executar: pnpm lint:fix"
    exit 1
fi

echo ""
echo "🎨 Verificando formatação..."
if pnpm format:check; then
    echo "✅ Formatação correta"
else
    echo "⚠️  Formatação incorreta, corrigindo automaticamente..."
    pnpm format:write
    echo "✅ Formatação corrigida"
fi

echo ""
echo "🔧 Verificando tipos TypeScript..."
if pnpm type-check; then
    echo "✅ Tipos corretos"
else
    echo "❌ Erros de tipo encontrados"
    echo "💡 Verifique os erros acima e corrija-os"
    exit 1
fi

echo ""
echo "🏗️  Fase 2: Build Completo"
echo "-------------------------"

echo "🔨 Executando build completo..."
if pnpm build; then
    echo "✅ Build completo passou"
else
    echo "❌ Build falhou"
    echo "💡 Tente executar: pnpm clean && pnpm install && pnpm build"
    exit 1
fi

echo ""
echo "📊 Verificando outputs do build..."
# Verificar se os diretórios dist foram criados
dist_count=$(find . -name "dist" -type d -not -path "./node_modules/*" | wc -l)
echo "   Diretórios dist criados: $dist_count"

if [ "$DOCKER_AVAILABLE" = true ]; then
    echo ""
    echo "🐳 Fase 3: Teste de Builds Docker"
    echo "--------------------------------"

    echo "🔨 Testando build Docker ai.api..."
    if pnpm docker:build:ai.api; then
        echo "✅ Docker build ai.api passou"
    else
        echo "❌ Docker build ai.api falhou"
        echo "💡 Verifique se o Docker está funcionando: docker --version"
        exit 1
    fi

    echo ""
    echo "🔨 Testando build Docker twilio.api..."
    if pnpm docker:build:twilio.api; then
        echo "✅ Docker build twilio.api passou"
    else
        echo "❌ Docker build twilio.api falhou"
        exit 1
    fi

    echo ""
    echo "🔨 Testando build Docker smarters.api..."
    if pnpm docker:build:smarters.api; then
        echo "✅ Docker build smarters.api passou"
    else
        echo "❌ Docker build smarters.api falhou"
        exit 1
    fi

    echo ""
    echo "📊 Verificando imagens Docker criadas..."
    docker_images=$(docker images | grep -c "lisa" || echo "0")
    echo "   Imagens Docker lisa encontradas: $docker_images"
else
    echo ""
    echo "⏭️  Fase 3: Testes Docker Pulados"
    echo "--------------------------------"
    echo "   Docker não está disponível"
fi

echo ""
echo "🧪 Fase 4: Testes Funcionais Básicos"
echo "-----------------------------------"

echo "🤖 Testando AI workflows..."
# Executar teste AI em background com timeout
timeout 30s pnpm ai &> /dev/null && echo "✅ AI workflows funcionam" || echo "⚠️  AI workflows podem ter problemas (timeout ou erro)"

echo ""
echo "📈 Resumo dos Resultados"
echo "======================="

# Criar log do teste
test_log=".test-general-history.log"
echo "$(date): Teste geral executado por $(whoami)" >> "$test_log"

echo "✅ Qualidade de Código:"
echo "   - Lint: ✅ Passou"
echo "   - Formatação: ✅ Passou"
echo "   - Tipos TypeScript: ✅ Passou"

echo ""
echo "✅ Build:"
echo "   - Build completo: ✅ Passou"
echo "   - Diretórios dist: $dist_count criados"

if [ "$DOCKER_AVAILABLE" = true ]; then
    echo ""
    echo "✅ Docker:"
    echo "   - ai.api: ✅ Passou"
    echo "   - twilio.api: ✅ Passou"
    echo "   - smarters.api: ✅ Passou"
    echo "   - Imagens criadas: $docker_images"
fi

echo ""
echo "✅ Funcional:"
echo "   - AI workflows: ✅ Testado"

echo ""
echo "🎉 TESTE GERAL COMPLETO - SUCESSO!"
echo "=================================="

# Mostrar tamanho atual do projeto
current_size=$(du -sh . 2>/dev/null | cut -f1)
echo "📊 Tamanho atual do projeto: $current_size"

echo ""
echo "📝 Próximos passos recomendados:"
echo "-------------------------------"
echo "   1. pnpm dev                 # Testar execução em desenvolvimento"
echo "   2. Verificar endpoints       # Testar APIs manualmente"
echo "   3. Testar clients           # Verificar interfaces web"
echo ""
echo "📚 Para mais informações:"
echo "   docs/maintenance/GENERAL_TESTING_GUIDE.md"
echo "   docs/setup/QUICK_START_GUIDE.md"
echo ""
echo "✨ Todos os testes automatizados passaram!"
echo "   O projeto está pronto para desenvolvimento/deploy."
