#!/bin/bash
# Script: clean-repository.sh
# Descrição: Limpeza geral completa do repositório Lisa Workspace
# Uso: bash scripts/clean-repository.sh

set -e  # Parar em caso de erro

echo "🧹 Iniciando limpeza geral do repositório Lisa Workspace..."
echo "=================================================="

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ] || [ ! -f "pnpm-workspace.yaml" ]; then
    echo "❌ Erro: Execute este script no diretório raiz do workspace"
    echo "   Certifique-se de estar em lisa.workspace/"
    exit 1
fi

# Verificar se pnpm está instalado
if ! command -v pnpm &> /dev/null; then
    echo "❌ Erro: pnpm não está instalado"
    echo "   Instale com: npm install -g pnpm@latest"
    exit 1
fi

# Verificar status do git
if [ -n "$(git status --porcelain)" ]; then
    echo "⚠️  Aviso: Há arquivos não commitados:"
    git status --short
    echo ""
    echo "Recomendamos fazer commit antes da limpeza."
    read -p "Continuar mesmo assim? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Limpeza cancelada pelo usuário"
        exit 1
    fi
fi

echo ""
echo "🗑️  Fase 1: Removendo dependências..."
echo "------------------------------------"

# Remover node_modules do root
if [ -d "node_modules" ]; then
    echo "   Removendo node_modules do root..."
    rm -rf node_modules
fi

# Remover todos os node_modules de subprojetos
echo "   Procurando node_modules em subprojetos..."
find . -name "node_modules" -type d -not -path "./node_modules/*" | while read dir; do
    echo "   Removendo: $dir"
    rm -rf "$dir"
done

echo ""
echo "🗑️  Fase 2: Removendo builds e artefatos..."
echo "-------------------------------------------"

# Remover dist do root
if [ -d "dist" ]; then
    echo "   Removendo dist do root..."
    rm -rf dist
fi

# Remover todos os dist de subprojetos
echo "   Procurando diretórios dist em subprojetos..."
find . -name "dist" -type d -not -path "./node_modules/*" | while read dir; do
    echo "   Removendo: $dir"
    rm -rf "$dir"
done

# Remover arquivos TypeScript build info
echo "   Removendo arquivos *.tsbuildinfo..."
find . -name "*.tsbuildinfo" -delete 2>/dev/null || true
find . -name "tsconfig.tsbuildinfo" -delete 2>/dev/null || true

echo ""
echo "🗑️  Fase 3: Removendo caches..."
echo "-------------------------------"

# Cache do Turbo
if [ -d ".turbo" ]; then
    echo "   Removendo cache do Turbo (.turbo)..."
    rm -rf .turbo
fi
find . -name ".turbo" -type d -not -path "./node_modules/*" | while read dir; do
    echo "   Removendo cache Turbo: $dir"
    rm -rf "$dir"
done

# Cache do Mastra
if [ -d ".mastra" ]; then
    echo "   Removendo cache do Mastra (.mastra)..."
    rm -rf .mastra
fi
find . -name ".mastra" -type d -not -path "./node_modules/*" | while read dir; do
    echo "   Removendo cache Mastra: $dir"
    rm -rf "$dir"
done

# Outros caches comuns
for cache_dir in ".next" ".vite" ".nuxt" ".output"; do
    if [ -d "$cache_dir" ]; then
        echo "   Removendo cache: $cache_dir"
        rm -rf "$cache_dir"
    fi
    find . -name "$cache_dir" -type d -not -path "./node_modules/*" | while read dir; do
        echo "   Removendo cache: $dir"
        rm -rf "$dir"
    done
done

echo ""
echo "🗑️  Fase 4: Removendo arquivos temporários..."
echo "---------------------------------------------"

# Arquivos de banco temporários
echo "   Removendo arquivos de banco temporários..."
find . -name "*.db" -not -path "./node_modules/*" -delete 2>/dev/null || true
find . -name "*.sqlite" -not -path "./node_modules/*" -delete 2>/dev/null || true

# Arquivos de log
echo "   Removendo arquivos de log..."
find . -name "*.log" -not -path "./node_modules/*" -delete 2>/dev/null || true
find . -name "npm-debug.log*" -not -path "./node_modules/*" -delete 2>/dev/null || true
find . -name "yarn-debug.log*" -not -path "./node_modules/*" -delete 2>/dev/null || true
find . -name "yarn-error.log*" -not -path "./node_modules/*" -delete 2>/dev/null || true

# Arquivos do sistema
echo "   Removendo arquivos temporários do sistema..."
find . -name ".DS_Store" -delete 2>/dev/null || true
find . -name "Thumbs.db" -delete 2>/dev/null || true
find . -name "*.tmp" -not -path "./node_modules/*" -delete 2>/dev/null || true

echo ""
echo "🗑️  Fase 5: Removendo coverage e arquivos de teste..."
echo "-----------------------------------------------------"

# Coverage de testes
if [ -d "coverage" ]; then
    echo "   Removendo coverage do root..."
    rm -rf coverage
fi
find . -name "coverage" -type d -not -path "./node_modules/*" | while read dir; do
    echo "   Removendo coverage: $dir"
    rm -rf "$dir"
done

# Arquivos de teste temporários
echo "   Removendo arquivos de teste temporários..."
find . -name "*.test.js.map" -not -path "./node_modules/*" -delete 2>/dev/null || true
find . -name "*.spec.js.map" -not -path "./node_modules/*" -delete 2>/dev/null || true

echo ""
echo "🧹 Fase 6: Limpando cache do pnpm..."
echo "-----------------------------------"

echo "   Executando pnpm store prune..."
pnpm store prune

echo ""
echo "✅ Limpeza completa finalizada!"
echo "=============================="

# Log da limpeza
echo "$(date): Limpeza geral executada por $(whoami)" >> .cleanup-history.log

echo ""
echo "📊 Verificação pós-limpeza:"
echo "---------------------------"

# Verificar se ainda há node_modules
remaining_node_modules=$(find . -name "node_modules" -type d 2>/dev/null | wc -l)
echo "   node_modules restantes: $remaining_node_modules"

# Verificar se ainda há dist
remaining_dist=$(find . -name "dist" -type d 2>/dev/null | wc -l)
echo "   diretórios dist restantes: $remaining_dist"

# Verificar se ainda há .tsbuildinfo
remaining_tsbuildinfo=$(find . -name "*.tsbuildinfo" 2>/dev/null | wc -l)
echo "   arquivos .tsbuildinfo restantes: $remaining_tsbuildinfo"

# Mostrar tamanho atual do diretório
current_size=$(du -sh . 2>/dev/null | cut -f1)
echo "   Tamanho atual do diretório: $current_size"

echo ""
echo "📝 Próximos passos recomendados:"
echo "-------------------------------"
echo "   1. pnpm install          # Reinstalar dependências"
echo "   2. pnpm build            # Rebuild do projeto"
echo "   3. Configurar .env files # Se necessário"
echo "   4. pnpm lint             # Verificar qualidade"
echo "   5. pnpm dev              # Testar desenvolvimento"
echo ""
echo "📚 Para mais informações, consulte:"
echo "   docs/maintenance/REPOSITORY_CLEANUP_GUIDE.md"
echo "   docs/setup/QUICK_START_GUIDE.md"
echo ""
echo "🎉 Limpeza concluída com sucesso!"
