{"name": "@lisa/agui.test-server", "version": "0.0.1", "description": "AGUI Test Server - Testing server for AGUI components", "private": true, "scripts": {"build": "nest build", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@lisa/common": "workspace:*", "@mastra/client-js": "^0.10.5", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/express": "^5.0.0", "@types/node": "^22.10.7", "jest": "^30.0.2", "ts-jest": "^29.2.5", "typescript": "^5.8.3"}}